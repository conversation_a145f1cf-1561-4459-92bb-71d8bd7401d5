#!/usr/bin/env node

/**
 * <PERSON><PERSON> Backend CLI
 * Command-line interface for managing Kilat Backend server
 */

const { KilatBackend } = require('../dist/index.js');
const { join } = require('path');
const { existsSync } = require('fs');

// CLI Commands
const commands = {
  start: startServer,
  dev: startDevServer,
  build: buildServer,
  test: testServer,
  routes: listRoutes,
  health: checkHealth,
  help: showHelp
};

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0] || 'help';
const options = parseOptions(args.slice(1));

// Execute command
if (commands[command]) {
  commands[command](options);
} else {
  console.error(`❌ Unknown command: ${command}`);
  showHelp();
  process.exit(1);
}

// 🚀 Start production server
async function startServer(options) {
  console.log('🚀 Starting Kilat Backend server...');
  
  const config = loadConfig(options);
  const server = new KilatBackend(config);
  
  try {
    await server.start();
    console.log(`✅ Server running on http://${config.host}:${config.port}`);
    
    // Graceful shutdown
    process.on('SIGTERM', async () => {
      console.log('🛑 Shutting down server...');
      await server.stop();
      process.exit(0);
    });
    
    process.on('SIGINT', async () => {
      console.log('🛑 Shutting down server...');
      await server.stop();
      process.exit(0);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// 🔧 Start development server with hot reload
async function startDevServer(options) {
  console.log('🔧 Starting Kilat Backend in development mode...');
  
  const config = loadConfig({
    ...options,
    logging: { level: 'debug', format: 'dev' },
    security: { helmet: false }
  });
  
  const server = new KilatBackend(config);
  
  try {
    await server.start();
    console.log(`✅ Development server running on http://${config.host}:${config.port}`);
    console.log(`📡 API endpoints available at http://${config.host}:${config.port}${config.apiPrefix}`);
    console.log('🔄 Watching for changes...');
    
    // In development, show more detailed logs
    if (process.env.NODE_ENV !== 'production') {
      setTimeout(async () => {
        const stats = server.getStats();
        console.log('📊 Server stats:', JSON.stringify(stats, null, 2));
      }, 2000);
    }
  } catch (error) {
    console.error('❌ Failed to start development server:', error.message);
    process.exit(1);
  }
}

// 🏗️ Build server for production
async function buildServer(options) {
  console.log('🏗️ Building Kilat Backend for production...');
  
  // TODO: Implement build process
  // This would compile TypeScript, optimize assets, etc.
  
  console.log('✅ Build completed successfully');
}

// 🧪 Test server endpoints
async function testServer(options) {
  console.log('🧪 Testing Kilat Backend endpoints...');
  
  const config = loadConfig(options);
  const server = new KilatBackend(config);
  
  try {
    await server.start();
    
    // Test health endpoint
    const response = await fetch(`http://${config.host}:${config.port}${config.apiPrefix}/health`);
    const health = await response.json();
    
    console.log('✅ Health check passed:', health.status);
    
    await server.stop();
    console.log('✅ All tests passed');
  } catch (error) {
    console.error('❌ Tests failed:', error.message);
    process.exit(1);
  }
}

// 📋 List all available routes
async function listRoutes(options) {
  console.log('📋 Listing Kilat Backend routes...');
  
  const config = loadConfig(options);
  const server = new KilatBackend(config);
  
  try {
    await server.start();
    
    const response = await fetch(`http://${config.host}:${config.port}${config.apiPrefix}/routes`);
    const data = await response.json();
    
    console.log('\n📡 Available Routes:');
    console.log('==================');
    
    data.routes.forEach(route => {
      const method = route.method.padEnd(6);
      const path = route.path.padEnd(30);
      const file = route.file === 'programmatic' ? '(built-in)' : route.file;
      console.log(`${method} ${path} → ${file}`);
    });
    
    await server.stop();
  } catch (error) {
    console.error('❌ Failed to list routes:', error.message);
    process.exit(1);
  }
}

// 🏥 Check server health
async function checkHealth(options) {
  console.log('🏥 Checking Kilat Backend health...');
  
  const config = loadConfig(options);
  
  try {
    const response = await fetch(`http://${config.host}:${config.port}${config.apiPrefix}/health`);
    const health = await response.json();
    
    console.log('\n🏥 Health Status:');
    console.log('================');
    console.log(`Status: ${health.status}`);
    console.log(`Uptime: ${Math.round(health.uptime / 1000)}s`);
    console.log(`Version: ${health.version}`);
    
    console.log('\n🔧 Services:');
    Object.entries(health.services).forEach(([service, status]) => {
      const icon = status === 'up' ? '✅' : '❌';
      console.log(`${icon} ${service}: ${status}`);
    });
    
    console.log('\n📊 Metrics:');
    console.log(`Requests: ${health.metrics.requests.total}`);
    console.log(`Success Rate: ${Math.round((health.metrics.requests.successful / health.metrics.requests.total) * 100)}%`);
    console.log(`Avg Response Time: ${Math.round(health.metrics.requests.averageResponseTime)}ms`);
    console.log(`Memory Usage: ${Math.round(health.metrics.memory.percentage)}%`);
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    console.log('💡 Make sure the server is running');
    process.exit(1);
  }
}

// ❓ Show help information
function showHelp() {
  console.log(`
⚡ Kilat Backend CLI

Usage: kilat-backend <command> [options]

Commands:
  start     Start production server
  dev       Start development server with hot reload
  build     Build server for production
  test      Test server endpoints
  routes    List all available routes
  health    Check server health status
  help      Show this help message

Options:
  --port <number>     Server port (default: 8080)
  --host <string>     Server host (default: localhost)
  --config <path>     Path to config file
  --api-prefix <path> API prefix (default: /api)
  --no-cors          Disable CORS
  --no-auth          Disable authentication
  --verbose          Enable verbose logging

Examples:
  kilat-backend start --port 3000
  kilat-backend dev --verbose
  kilat-backend routes --config ./my-config.js
  kilat-backend health

For more information, visit: https://kilatjs.dev/docs/backend
`);
}

// 🔧 Load configuration
function loadConfig(options) {
  let config = {
    port: 8080,
    host: 'localhost',
    apiPrefix: '/api',
    autoStart: false
  };
  
  // Load from config file if specified
  if (options.config) {
    const configPath = join(process.cwd(), options.config);
    if (existsSync(configPath)) {
      try {
        const fileConfig = require(configPath);
        config = { ...config, ...fileConfig };
      } catch (error) {
        console.error(`❌ Failed to load config file: ${error.message}`);
        process.exit(1);
      }
    } else {
      console.error(`❌ Config file not found: ${configPath}`);
      process.exit(1);
    }
  }
  
  // Override with command line options
  if (options.port) config.port = parseInt(options.port);
  if (options.host) config.host = options.host;
  if (options.apiPrefix) config.apiPrefix = options.apiPrefix;
  
  // Handle boolean flags
  if (options.noCors) {
    config.cors = { enabled: false };
  }
  
  if (options.noAuth) {
    config.auth = { enabled: false };
  }
  
  if (options.verbose) {
    config.logging = { level: 'debug', format: 'dev' };
  }
  
  return config;
}

// 🔧 Parse command line options
function parseOptions(args) {
  const options = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg.startsWith('--')) {
      const key = arg.substring(2);
      
      if (key.startsWith('no-')) {
        // Boolean flag (--no-cors)
        const flagName = key.substring(3).replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        options[`no${flagName.charAt(0).toUpperCase()}${flagName.slice(1)}`] = true;
      } else if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
        // Key-value pair (--port 3000)
        const value = args[i + 1];
        const optionName = key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        options[optionName] = value;
        i++; // Skip next argument
      } else {
        // Boolean flag (--verbose)
        const optionName = key.replace(/-([a-z])/g, (g) => g[1].toUpperCase());
        options[optionName] = true;
      }
    }
  }
  
  return options;
}
