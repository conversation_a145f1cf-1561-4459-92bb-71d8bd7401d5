import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import { join } from 'path';
import { ensureDir, pathExists, writeFile, copy } from 'fs-extra';
import { execa } from 'execa';
import type { 
  KilatProject, 
  ProjectTemplate, 
  CommandOptions,
  KilatTheme,
  ProjectFeature,
  GeneratorContext
} from '../types';
import { detectPackageManager, installDependencies } from '../utils/package-manager';
import { generateProjectFiles } from '../utils/generator';
import { validateProjectName } from '../utils/validation';

/**
 * Create command - Interactive project generator
 * Creates new Kilat.js projects with customizable templates and features
 */

export async function createCommand(projectName?: string, options: CommandOptions = {}) {
  console.log(chalk.cyan.bold('\n⚡ Welcome to Kilat.js Project Generator!\n'));

  try {
    // Get project configuration through interactive prompts
    const config = await getProjectConfig(projectName, options);
    
    // Validate and create project
    await createProject(config, options);
    
    console.log(chalk.green.bold('\n🎉 Project created successfully!\n'));
    showNextSteps(config);
    
  } catch (error) {
    console.error(chalk.red.bold('\n❌ Failed to create project:'), error.message);
    process.exit(1);
  }
}

// 📝 Get project configuration through interactive prompts
async function getProjectConfig(projectName?: string, options: CommandOptions): Promise<KilatProject> {
  const questions = [];

  // Project name
  if (!projectName) {
    questions.push({
      type: 'input',
      name: 'name',
      message: 'What is your project name?',
      default: 'my-kilat-app',
      validate: validateProjectName
    });
  }

  // Platform selection
  if (!options.template) {
    questions.push({
      type: 'list',
      name: 'platform',
      message: 'Which platform do you want to target?',
      choices: [
        { name: '🌐 Web Application (React + Vite)', value: 'web' },
        { name: '🖥️  Desktop Application (Electron)', value: 'desktop' },
        { name: '📱 Mobile Application (Expo)', value: 'mobile' },
        { name: '🚀 Fullstack Application (Web + API)', value: 'fullstack' }
      ],
      default: 'web'
    });
  }

  // Theme selection
  if (!options.theme) {
    questions.push({
      type: 'list',
      name: 'theme',
      message: 'Choose your UI theme:',
      choices: [
        { name: '🔮 Cyberpunk - Neon glow futuristic', value: 'cyberpunk' },
        { name: '🇮🇩 Nusantara - Indonesian cultural colors', value: 'nusantara' },
        { name: '🕹️  Retro - 90s arcade pixel art', value: 'retro' },
        { name: '🧱 Material - Google Material Design', value: 'material' },
        { name: '🧼 Neumorphism - Soft shadow inset', value: 'neumorphism' },
        { name: '🌈 Aurora - Gradient blur pastels', value: 'aurora' },
        { name: '⚪ Minimalist - Clean flat UI', value: 'minimalist' }
      ],
      default: 'cyberpunk'
    });
  }

  // Database selection
  if (!options.database) {
    questions.push({
      type: 'list',
      name: 'database',
      message: 'Choose your database:',
      choices: [
        { name: '📦 SQLite - Local file database (recommended)', value: 'sqlite' },
        { name: '🐬 MySQL - Production database', value: 'mysql' },
        { name: '🚫 None - No database', value: 'none' }
      ],
      default: 'sqlite'
    });
  }

  // Features selection
  if (!options.features) {
    questions.push({
      type: 'checkbox',
      name: 'features',
      message: 'Select additional features:',
      choices: [
        { name: '🔐 Authentication (JWT + bcrypt)', value: 'auth', checked: true },
        { name: '🌐 Server-Side Rendering (SSR)', value: 'ssr' },
        { name: '📱 Progressive Web App (PWA)', value: 'pwa' },
        { name: '🧪 Testing Setup (Vitest + Playwright)', value: 'testing' },
        { name: '🐳 Docker Configuration', value: 'docker' },
        { name: '🚀 CI/CD GitHub Actions', value: 'ci-cd' },
        { name: '📊 Analytics & Monitoring', value: 'monitoring' },
        { name: '🌍 Internationalization (i18n)', value: 'i18n' }
      ]
    });
  }

  // Plugin selection
  if (!options.plugins) {
    questions.push({
      type: 'checkbox',
      name: 'plugins',
      message: 'Select plugins to install:',
      choices: [
        { name: '📝 CMS Plugin - Content management', value: 'cms' },
        { name: '💳 Payments Plugin - Xendit, Midtrans', value: 'payments' },
        { name: '📤 Upload Plugin - File handling', value: 'upload', checked: true },
        { name: '🤖 AI Assistant Plugin - GPT integration', value: 'ai' },
        { name: '💬 Chat Plugin - Real-time messaging', value: 'chat' },
        { name: '📧 Email Plugin - SMTP & templates', value: 'email' }
      ]
    });
  }

  const answers = await inquirer.prompt(questions);

  return {
    name: projectName || answers.name,
    platform: options.template?.includes('web') ? 'web' : 
              options.template?.includes('desktop') ? 'desktop' :
              options.template?.includes('mobile') ? 'mobile' :
              options.template?.includes('fullstack') ? 'fullstack' :
              answers.platform,
    theme: options.theme || answers.theme,
    database: options.database || answers.database,
    plugins: options.plugins || answers.plugins || [],
    features: options.features || answers.features || [],
    template: options.template || `kilat-${answers.platform}`,
    directory: join(process.cwd(), projectName || answers.name)
  };
}

// 🏗️ Create the project
async function createProject(config: KilatProject, options: CommandOptions) {
  const spinner = ora('Creating project structure...').start();

  try {
    // Check if directory already exists
    if (await pathExists(config.directory)) {
      if (!options.force) {
        spinner.fail('Directory already exists. Use --force to overwrite.');
        process.exit(1);
      }
    }

    // Create project directory
    await ensureDir(config.directory);
    spinner.text = 'Setting up project structure...';

    // Get template
    const template = await getTemplate(config.template, config.platform);
    
    // Generate project files
    const context: GeneratorContext = {
      projectName: config.name,
      projectPath: config.directory,
      template,
      config,
      packageManager: await detectPackageManager(),
      variables: {
        projectName: config.name,
        theme: config.theme,
        database: config.database,
        features: config.features,
        plugins: config.plugins,
        timestamp: new Date().toISOString(),
        kilatVersion: '1.0.0'
      }
    };

    await generateProjectFiles(context);
    spinner.text = 'Installing dependencies...';

    // Install dependencies
    if (!options.dry) {
      await installDependencies(config.directory, context.packageManager);
    }

    // Initialize git repository
    if (!options.dry) {
      await initGitRepository(config.directory);
    }

    // Run post-install scripts
    await runPostInstallScripts(config, context);

    spinner.succeed('Project created successfully!');

  } catch (error) {
    spinner.fail('Failed to create project');
    throw error;
  }
}

// 📋 Get project template
async function getTemplate(templateName: string, platform: string): Promise<ProjectTemplate> {
  // For now, return a basic template structure
  // In production, this would fetch from a template registry
  
  const baseTemplate: ProjectTemplate = {
    name: templateName,
    description: `Kilat.js ${platform} application`,
    platform: platform as any,
    features: [],
    dependencies: [
      'kilat-core',
      'kilatcss',
      'kilatanim.js',
      'kilat-router',
      'kilat-utils'
    ],
    devDependencies: [
      'typescript',
      'vite',
      '@types/node'
    ],
    scripts: {
      'dev': 'kilat dev',
      'build': 'kilat build',
      'start': 'kilat start',
      'test': 'kilat test',
      'lint': 'kilat lint'
    },
    files: []
  };

  // Customize based on platform
  switch (platform) {
    case 'web':
      baseTemplate.dependencies.push('react', 'react-dom');
      baseTemplate.devDependencies.push('@types/react', '@types/react-dom');
      break;
    case 'desktop':
      baseTemplate.dependencies.push('electron');
      baseTemplate.devDependencies.push('@types/electron');
      break;
    case 'mobile':
      baseTemplate.dependencies.push('expo', 'react-native');
      break;
    case 'fullstack':
      baseTemplate.dependencies.push('kilat-backend', 'kilat-db');
      break;
  }

  return baseTemplate;
}

// 🔧 Initialize git repository
async function initGitRepository(projectPath: string) {
  try {
    await execa('git', ['init'], { cwd: projectPath });
    await execa('git', ['add', '.'], { cwd: projectPath });
    await execa('git', ['commit', '-m', 'Initial commit from Kilat.js CLI'], { cwd: projectPath });
  } catch (error) {
    // Git initialization is optional
    console.log(chalk.yellow('⚠️  Could not initialize git repository'));
  }
}

// 🎯 Run post-install scripts
async function runPostInstallScripts(config: KilatProject, context: GeneratorContext) {
  // Database setup
  if (config.database !== 'none') {
    const spinner = ora('Setting up database...').start();
    try {
      // Create database configuration
      await createDatabaseConfig(config, context);
      
      // Run initial migration if SQLite
      if (config.database === 'sqlite') {
        await execa('kilat', ['db', 'migrate'], { cwd: config.directory });
      }
      
      spinner.succeed('Database configured');
    } catch (error) {
      spinner.warn('Database setup skipped (can be configured later)');
    }
  }

  // Plugin installation
  if (config.plugins.length > 0) {
    const spinner = ora('Installing plugins...').start();
    try {
      for (const plugin of config.plugins) {
        await execa('kilat', ['plugin', 'add', plugin], { cwd: config.directory });
      }
      spinner.succeed('Plugins installed');
    } catch (error) {
      spinner.warn('Some plugins could not be installed');
    }
  }
}

// 🗄️ Create database configuration
async function createDatabaseConfig(config: KilatProject, context: GeneratorContext) {
  const dbConfig = {
    driver: config.database,
    connection: config.database === 'sqlite' 
      ? { sqlite: { file: './data.db', enableWAL: true } }
      : { mysql: { host: 'localhost', port: 3306, user: 'root', password: '', database: config.name } },
    migrations: {
      directory: './migrations',
      autoRun: true
    }
  };

  const configPath = join(config.directory, 'kilat.config.ts');
  // Update the config file with database settings
  // This would be handled by the file generator
}

// 📋 Show next steps
function showNextSteps(config: KilatProject) {
  console.log(chalk.cyan.bold('🚀 Next Steps:\n'));
  
  console.log(chalk.white(`1. Navigate to your project:`));
  console.log(chalk.gray(`   cd ${config.name}\n`));
  
  console.log(chalk.white(`2. Start development server:`));
  console.log(chalk.gray(`   kilat dev\n`));
  
  console.log(chalk.white(`3. Open in browser:`));
  console.log(chalk.gray(`   http://localhost:3000\n`));
  
  if (config.database !== 'none') {
    console.log(chalk.white(`4. Configure database:`));
    console.log(chalk.gray(`   kilat db setup\n`));
  }
  
  if (config.plugins.length > 0) {
    console.log(chalk.white(`5. Configure plugins:`));
    config.plugins.forEach(plugin => {
      console.log(chalk.gray(`   kilat plugin configure ${plugin}`));
    });
    console.log();
  }
  
  console.log(chalk.cyan('📚 Documentation: https://kilatjs.dev/docs'));
  console.log(chalk.cyan('💬 Community: https://github.com/kangpcode/kilatjs/discussions'));
  console.log(chalk.cyan('🐛 Issues: https://github.com/kangpcode/kilatjs/issues\n'));
}
